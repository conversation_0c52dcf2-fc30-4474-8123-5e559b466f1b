#pragma once

#include "state.h"
#include <vector>
#include <memory>

class Dfa
{
    private:
        std::vector<std::shared_ptr<State>> m_states;
        std::weak_ptr<State> startState;
        Dfa() {};
        Dfa(uint32_t id) {};

        class State
        {
            private:
                static uint32_t count;

                uint32_t m_id;
                TransitionMap m_transitions;

                State();
                uint32_t getId() const;
                bool addTransition(Alphabet letter, std::weak_ptr<State> state);
                bool removeTransition(Alphabet letter);
        };

        static uint32_t idCount;

    public:
        static Dfa& getInstance()
        {
            static Dfa instance; // Guaranteed to be destroyed. Instantiated on first use.
            return instance;
        }
        Dfa(const Dfa& other) = delete;
        void operator=(const Dfa& other) = delete;

        uint32_t createState();
};
