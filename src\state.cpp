#include "state.h"

#include <cstddef>

// Define the static member variable
uint32_t State::count = 0;

State::State()
{
    m_id = count++;
}

uint32_t State::getId() const
{
    return m_id;
}

bool State::addTransition(Alphabet letter, std::weak_ptr<State> state)
{
    bool ret = false;
    auto found = m_transitions.find(letter);

    if (found != m_transitions.end() && !(found->second.expired()))
    {
        ret = true;
    }

    m_transitions[letter] = state;

    return ret;
}

bool State::removeTransition(Alphabet letter)
{
    return m_transitions.erase(letter) != 0;
}