#include "state.h"

#include <cstddef>

State::State()
{
    m_id = count++;
}

uint32_t State::getId() const
{
    return m_id;
}

bool State::addTransition(Alphabet letter, std::weak_ptr<State> state)
{
    auto found = m_transitions.find(letter);
    bool ret = false;
    if (found != m_transitions.end() && !(found->second.expired()))
    {
        ret = true;
    }

    m_transitions[letter] = state;

    return ret;
}