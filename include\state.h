#pragma once

#include <iostream>
#include <map>
#include <memory>

#include "alphabet.h"

class State;

typedef std::map<Alphabet, std::weak_ptr<State>> TransitionMap;

class State
{
    private:
        static uint32_t count;

        uint32_t m_id;
        TransitionMap m_transitions;
    public:
        State();
        uint32_t getId() const;
        bool addTransition(Alphabet letter, std::weak_ptr<State> state);
};
